"""
End-to-end tests for plugin.yaml router configuration feature.

These tests validate that routers defined in plugin.yaml files are correctly
registered and handle requests as expected.
"""
import tempfile
import yaml
import pytest
import shutil
from pathlib import Path
from typing import Dict, Any, Optional

from serv.plugins import Plugin
from serv.plugins.loader import PluginSpec
from serv.plugins.router_plugin import RouterPlugin

from tests.e2e.helpers import create_test_client


def create_plugin_with_yaml_config(plugin_config: Dict[str, Any], handlers_code: Optional[str] = None) -> tuple[Plugin, Path]:
    """
    Helper to create a plugin with a specific plugin.yaml configuration.
    
    Args:
        plugin_config: The configuration to write to plugin.yaml
        handlers_code: Optional Python code for handlers module
        
    Returns:
        Tuple of (plugin_instance, temp_directory_path)
    """
    # Create temporary directory for the plugin
    temp_dir = tempfile.mkdtemp()
    plugin_dir = Path(temp_dir)
    
    try:
        # Create plugin.yaml file
        with open(plugin_dir / "plugin.yaml", "w") as f:
            yaml.dump(plugin_config, f)
        
        # Create __init__.py to make it a package
        (plugin_dir / "__init__.py").touch()
        
        # Create handlers.py if provided
        if handlers_code:
            with open(plugin_dir / "handlers.py", "w") as f:
                f.write(handlers_code)
        
        # Add the plugin directory to sys.path so handlers can be imported
        import sys
        if str(plugin_dir) not in sys.path:
            sys.path.insert(0, str(plugin_dir))
        
        # Create the plugin spec and plugin
        spec = PluginSpec.from_path(plugin_dir, {})
        plugin = RouterPlugin(plugin_spec=spec, stand_alone=True)
        
        return plugin, plugin_dir
        
    except Exception:
        shutil.rmtree(temp_dir)
        raise


@pytest.mark.asyncio
async def test_basic_router_configuration():
    """Test basic router configuration with simple routes."""
    plugin_config = {
        "name": "Basic Router Plugin",
        "description": "A plugin with basic router configuration",
        "version": "1.0.0",
        "author": "Test Author",
        "routers": [
            {
                "name": "main_router",
                "config": {},
                "routes": [
                    {
                        "path": "/hello",
                        "handler": "handlers:hello_handler",
                        "methods": ["GET"],
                        "config": {}
                    },
                    {
                        "path": "/goodbye",
                        "handler": "handlers:goodbye_handler",
                        "methods": ["GET", "POST"],
                        "config": {}
                    }
                ]
            }
        ]
    }
    
    handlers_code = '''
from bevy import dependency
from serv.responses import ResponseBuilder

async def hello_handler(response: ResponseBuilder = dependency()):
    response.content_type("text/plain")
    response.body("Hello, World!")

async def goodbye_handler(response: ResponseBuilder = dependency()):
    response.content_type("text/plain")
    response.body("Goodbye!")
'''
    
    plugin, plugin_dir = create_plugin_with_yaml_config(plugin_config, handlers_code)
    
    try:
        async with create_test_client(plugins=[plugin]) as client:
            # Test GET /hello
            response = await client.get("/hello")
            assert response.status_code == 200
            assert response.text == "Hello, World!"
            
            # Test GET /goodbye
            response = await client.get("/goodbye")
            assert response.status_code == 200
            assert response.text == "Goodbye!"
            
            # Test POST /goodbye
            response = await client.post("/goodbye")
            assert response.status_code == 200
            assert response.text == "Goodbye!"
            
            # Test non-existent route
            response = await client.get("/not-found")
            assert response.status_code == 404
    finally:
        shutil.rmtree(plugin_dir)


@pytest.mark.asyncio
async def test_mounted_routers():
    """Test router mounting functionality."""
    plugin_config = {
        "name": "Mounted Router Plugin",
        "description": "A plugin with mounted routers",
        "version": "1.0.0",
        "author": "Test Author",
        "routers": [
            {
                "name": "api_v1_router",
                "mount": "/api/v1",
                "config": {"version": "1.0"},
                "routes": [
                    {
                        "path": "/users",
                        "handler": "handlers:users_v1_handler",
                        "methods": ["GET"],
                        "config": {"version": "v1"}
                    }
                ]
            },
            {
                "name": "api_v2_router",
                "mount": "/api/v2",
                "config": {"version": "2.0"},
                "routes": [
                    {
                        "path": "/users",
                        "handler": "handlers:users_v2_handler",
                        "methods": ["GET"],
                        "config": {"version": "v2"}
                    }
                ]
            }
        ]
    }
    
    handlers_code = '''
from bevy import dependency
from serv.responses import ResponseBuilder
from typing import Annotated

async def users_v1_handler(response: ResponseBuilder = dependency(), version: Annotated[str, "version"] = dependency()):
    response.content_type("application/json")
    response.body(f'{{"version": "{version}", "users": ["user1", "user2"]}}')

async def users_v2_handler(response: ResponseBuilder = dependency(), version: Annotated[str, "version"] = dependency()):
    response.content_type("application/json")
    response.body(f'{{"version": "{version}", "users": ["user1", "user2", "user3"]}}')
'''
    
    plugin, plugin_dir = create_plugin_with_yaml_config(plugin_config, handlers_code)
    
    try:
        async with create_test_client(plugins=[plugin]) as client:
            # Test v1 API
            response = await client.get("/api/v1/users")
            assert response.status_code == 200
            data = response.json()
            assert data["version"] == "v1"
            assert len(data["users"]) == 2
            
            # Test v2 API
            response = await client.get("/api/v2/users")
            assert response.status_code == 200
            data = response.json()
            assert data["version"] == "v2"
            assert len(data["users"]) == 3
            
            # Test non-existent version
            response = await client.get("/api/v3/users")
            assert response.status_code == 404
    finally:
        shutil.rmtree(plugin_dir)


@pytest.mark.asyncio
async def test_route_settings_injection():
    """Test that route settings are properly injected into handlers."""
    plugin_config = {
        "name": "Settings Injection Plugin",
        "description": "A plugin testing settings injection",
        "version": "1.0.0",
        "author": "Test Author",
        "routers": [
            {
                "name": "settings_router",
                "config": {"global_setting": "global_value"},
                "routes": [
                    {
                        "path": "/config-test",
                        "handler": "handlers:config_handler",
                        "methods": ["GET"],
                        "config": {
                            "route_setting": "route_value",
                            "message": "Hello from settings!"
                        }
                    }
                ]
            }
        ]
    }
    
    handlers_code = '''
from bevy import dependency
from serv.responses import ResponseBuilder
from typing import Annotated

async def config_handler(
    response: ResponseBuilder = dependency(),
    message: Annotated[str, "message"] = dependency(),
    global_setting: Annotated[str, "global_setting"] = dependency(),
    route_setting: Annotated[str, "route_setting"] = dependency()
):
    response.content_type("application/json")
    response.body(f'{{"message": "{message}", "global": "{global_setting}", "route": "{route_setting}"}}')
'''
    
    plugin, plugin_dir = create_plugin_with_yaml_config(plugin_config, handlers_code)
    
    try:
        async with create_test_client(plugins=[plugin]) as client:
            response = await client.get("/config-test")
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "Hello from settings!"
            assert data["global"] == "global_value"
            assert data["route"] == "route_value"
    finally:
        shutil.rmtree(plugin_dir)


@pytest.mark.asyncio
async def test_multiple_plugins_with_routers():
    """Test that multiple plugins with routers can coexist."""
    plugin1_config = {
        "name": "Plugin 1",
        "description": "First plugin",
        "version": "1.0.0",
        "author": "Test Author",
        "routers": [
            {
                "name": "plugin1_router",
                "config": {},
                "routes": [
                    {
                        "path": "/plugin1",
                        "handler": "handlers:plugin1_handler",
                        "methods": ["GET"],
                        "config": {"source": "plugin1"}
                    }
                ]
            }
        ]
    }
    
    plugin2_config = {
        "name": "Plugin 2",
        "description": "Second plugin",
        "version": "1.0.0",
        "author": "Test Author",
        "routers": [
            {
                "name": "plugin2_router",
                "config": {},
                "routes": [
                    {
                        "path": "/plugin2",
                        "handler": "handlers:plugin2_handler",
                        "methods": ["GET"],
                        "config": {"source": "plugin2"}
                    }
                ]
            }
        ]
    }
    
    handlers1_code = '''
from bevy import dependency
from serv.responses import ResponseBuilder
from typing import Annotated

async def plugin1_handler(response: ResponseBuilder = dependency(), source: Annotated[str, "source"] = dependency()):
    response.content_type("application/json")
    response.body(f'{{"handler": "plugin1", "source": "{source}"}}')
'''
    
    handlers2_code = '''
from bevy import dependency
from serv.responses import ResponseBuilder
from typing import Annotated

async def plugin2_handler(response: ResponseBuilder = dependency(), source: Annotated[str, "source"] = dependency()):
    response.content_type("application/json")
    response.body(f'{{"handler": "plugin2", "source": "{source}"}}')
'''
    
    plugin1, plugin1_dir = create_plugin_with_yaml_config(plugin1_config, handlers1_code)
    plugin2, plugin2_dir = create_plugin_with_yaml_config(plugin2_config, handlers2_code)
    
    try:
        async with create_test_client(plugins=[plugin1, plugin2]) as client:
            # Test plugin1's route
            response = await client.get("/plugin1")
            assert response.status_code == 200
            data = response.json()
            assert data["handler"] == "plugin1"
            assert data["source"] == "plugin1"
            
            # Test plugin2's route
            response = await client.get("/plugin2")
            assert response.status_code == 200
            data = response.json()
            assert data["handler"] == "plugin2"
            assert data["source"] == "plugin2"
    finally:
        shutil.rmtree(plugin1_dir)
        shutil.rmtree(plugin2_dir)